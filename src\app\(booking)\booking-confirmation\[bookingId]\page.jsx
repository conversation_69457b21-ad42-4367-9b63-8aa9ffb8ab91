'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

export default function BookingConfirmationPage() {
  const params = useParams();
  const router = useRouter();
  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (params.bookingId) {
      fetchBooking();
    }
  }, [params.bookingId]);

  const fetchBooking = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/bookings/${params.bookingId}`);
      const data = await response.json();

      if (data.success) {
        setBooking(data.data);
      } else {
        setError(data.message || 'Booking not found');
      }
    } catch (err) {
      console.error('Error fetching booking:', err);
      setError('Failed to load booking details');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getCustomerName = () => {
    if (booking?.customer?.firstname && booking?.customer?.surname) {
      return `${booking.customer.firstname} ${booking.customer.surname}`;
    }
    return booking?.customer?.name || 'Guest';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => router.push('/booking')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Make New Booking
          </button>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Booking Not Found</h1>
          <p className="text-gray-600 mb-6">The booking confirmation you're looking for doesn't exist.</p>
          <button
            onClick={() => router.push('/booking')}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Make New Booking
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-3xl mx-auto px-4">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="text-6xl text-green-600 mb-4">✅</div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Booking Confirmed!</h1>
          <p className="text-lg text-gray-600">
            Thank you for your booking. Your reservation has been confirmed and payment processed successfully.
          </p>
        </div>

        {/* Booking Details Card */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
          {/* Header */}
          <div className="bg-green-600 text-white p-6">
            <h2 className="text-xl font-bold">Booking Confirmation</h2>
            <p className="text-green-100">Booking #{booking.bookingNumber}</p>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Guest Information */}
            <div className="mb-6">
              <div className="flex items-center mb-3">
                <span className="text-gray-400 mr-2">👤</span>
                <h3 className="text-lg font-semibold text-gray-900">Guest Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                <div>
                  <p className="text-sm text-gray-600">Name</p>
                  <p className="font-medium">{getCustomerName()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <p className="font-medium">{booking.customer.email}</p>
                </div>
                {booking.customer.phone && (
                  <div>
                    <p className="text-sm text-gray-600">Phone</p>
                    <p className="font-medium">{booking.customer.phone}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm text-gray-600">Guests</p>
                  <p className="font-medium">{booking.numberOfGuests} guest{booking.numberOfGuests !== 1 ? 's' : ''}</p>
                </div>
              </div>
            </div>

            {/* Booking Details */}
            <div className="mb-6">
              <div className="flex items-center mb-3">
                <span className="text-gray-400 mr-2">📅</span>
                <h3 className="text-lg font-semibold text-gray-900">Booking Details</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                <div>
                  <p className="text-sm text-gray-600">Package</p>
                  <p className="font-medium">{booking.package.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Category</p>
                  <p className="font-medium capitalize">{booking.package.category}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Check-in</p>
                  <p className="font-medium">{formatDate(booking.dates.checkIn)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Check-out</p>
                  <p className="font-medium">{formatDate(booking.dates.checkOut)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Duration</p>
                  <p className="font-medium">{booking.dates.duration || 1} night{(booking.dates.duration || 1) !== 1 ? 's' : ''}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Status</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {booking.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Payment Information */}
            <div className="mb-6">
              <div className="flex items-center mb-3">
                <span className="text-gray-400 mr-2">💳</span>
                <h3 className="text-lg font-semibold text-gray-900">Payment Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                <div>
                  <p className="text-sm text-gray-600">Total Amount</p>
                  <p className="font-bold text-lg text-green-600">{formatCurrency(booking.pricing.totalAmount)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Payment Status</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {booking.payment.status}
                  </span>
                </div>
                {booking.payment.paidAt && (
                  <div>
                    <p className="text-sm text-gray-600">Payment Date</p>
                    <p className="font-medium">{formatDate(booking.payment.paidAt)}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm text-gray-600">Payment Method</p>
                  <p className="font-medium">Credit Card (Stripe)</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">What's Next?</h3>
          <ul className="space-y-2 text-blue-800">
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              You will receive a confirmation email shortly with all booking details
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              A reminder email will be sent 24 hours before your check-in date
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              Please arrive at the lodge by 3:00 PM on your check-in date
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">•</span>
              Contact us at (+267)76123456 if you have any questions
            </li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={() => window.print()}
            className="flex-1 bg-gray-600 text-white py-3 px-6 rounded-md font-medium hover:bg-gray-700 transition-colors"
          >
            Print Confirmation
          </button>
          <button
            onClick={() => router.push('/')}
            className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-md font-medium hover:bg-blue-700 transition-colors"
          >
            Back to Home
          </button>
        </div>

        {/* Contact Information */}
        <div className="text-center mt-8 text-gray-600">
          <p className="text-sm">
            Need help? Contact us at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
            {' '}or call{' '}
            <a href="tel:+26776123456" className="text-blue-600 hover:underline">
              (+267)76123456
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
