import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Payment } from '@/models/Payment';
import { Booking } from '@/models/Booking';
import { Package } from '@/models/Package';
import { requireManagerAPI } from '@/lib/auth-utils';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// GET /api/payments - Get payments from bookings (manager/admin only)
export const GET = requireManagerAPI(async (request) => {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const bookingId = searchParams.get('booking');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit')) || 20;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';

    // Build query for bookings with payment information
    const query = {};

    // Only get bookings that have payment information
    query['payment.status'] = { $exists: true };

    if (status) {
      query['payment.status'] = status;
    }

    if (bookingId) {
      query._id = bookingId;
    }

    if (startDate || endDate) {
      const dateFilter = {};
      if (startDate) {
        dateFilter.$gte = new Date(startDate);
      }
      if (endDate) {
        dateFilter.$lte = new Date(endDate);
      }
      query['payment.paidAt'] = dateFilter;
    }

    // Search functionality
    if (search) {
      query.$or = [
        { bookingNumber: { $regex: search, $options: 'i' } },
        { 'customer.email': { $regex: search, $options: 'i' } },
        { 'customer.firstname': { $regex: search, $options: 'i' } },
        { 'customer.surname': { $regex: search, $options: 'i' } },
        { 'payment.stripePaymentIntentId': { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const bookings = await Booking.find(query)
      .populate('package', 'name category')
      .sort({ 'payment.paidAt': -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Booking.countDocuments(query);

    // Transform booking data to payment format
    const payments = bookings.map(booking => ({
      _id: booking._id,
      paymentId: booking.payment.stripePaymentIntentId || booking._id,
      bookingId: booking._id,
      bookingNumber: booking.bookingNumber,
      amount: booking.pricing.totalAmount,
      currency: 'USD',
      status: booking.payment.status,
      method: booking.payment.method || 'card',
      customer: {
        name: booking.customer.firstname && booking.customer.surname
          ? `${booking.customer.firstname} ${booking.customer.surname}`
          : booking.customer.name || 'Unknown',
        email: booking.customer.email,
        phone: booking.customer.phone
      },
      booking: {
        bookingNumber: booking.bookingNumber,
        package: booking.package,
        checkIn: booking.dates.checkIn,
        checkOut: booking.dates.checkOut,
        guests: booking.numberOfGuests
      },
      fees: {
        stripeFee: booking.payment.stripeFee || 0,
        applicationFee: booking.payment.applicationFee || 0,
        totalFees: (booking.payment.stripeFee || 0) + (booking.payment.applicationFee || 0)
      },
      refunds: booking.payment.refunds || [],
      createdAt: booking.payment.paidAt || booking.createdAt,
      updatedAt: booking.updatedAt
    }));

    return NextResponse.json({
      success: true,
      data: payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch payments',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// POST /api/payments - Create payment intent
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { bookingId, amount, currency = 'usd', paymentMethodId } = body;
    
    // Validate required fields
    if (!bookingId || !amount) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'bookingId and amount are required',
        },
        { status: 400 }
      );
    }
    
    // Get booking details
    const booking = await Booking.findById(bookingId)
      .populate('customer', 'name email')
      .populate('package', 'name');
    
    if (!booking) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Booking not found',
        },
        { status: 404 }
      );
    }
    
    // Create or retrieve Stripe customer
    let stripeCustomer;
    const existingPayment = await Payment.findOne({
      customer: booking.customer._id,
      'stripe.customerId': { $exists: true },
    });
    
    if (existingPayment?.stripe?.customerId) {
      stripeCustomer = await stripe.customers.retrieve(existingPayment.stripe.customerId);
    } else {
      stripeCustomer = await stripe.customers.create({
        email: booking.customer.email,
        name: booking.customer.name,
        metadata: {
          userId: booking.customer._id.toString(),
        },
      });
    }
    
    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      customer: stripeCustomer.id,
      payment_method: paymentMethodId,
      confirmation_method: 'manual',
      confirm: !!paymentMethodId,
      metadata: {
        bookingId: booking._id.toString(),
        bookingNumber: booking.bookingNumber,
        packageName: booking.package.name,
      },
    });
    
    // Create payment record
    const payment = new Payment({
      booking: booking._id,
      customer: booking.customer._id,
      amount: amount,
      currency: currency.toUpperCase(),
      paymentMethod: {
        type: 'card',
      },
      stripe: {
        paymentIntentId: paymentIntent.id,
        customerId: stripeCustomer.id,
        paymentMethodId: paymentMethodId,
      },
      status: paymentIntent.status === 'succeeded' ? 'succeeded' : 'pending',
      metadata: {
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        source: 'website',
      },
    });
    
    await payment.save();
    
    // Update booking payment status if payment succeeded
    if (paymentIntent.status === 'succeeded') {
      await booking.updatePaymentStatus('paid', amount);
    }
    
    return NextResponse.json({
      success: true,
      data: {
        payment,
        clientSecret: paymentIntent.client_secret,
        paymentIntent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
        },
      },
      message: 'Payment intent created successfully',
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    
    if (error.type === 'StripeCardError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment Error',
          message: error.message,
          code: error.code,
        },
        { status: 402 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create payment',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/payments - Process refund (manager/admin only)
export const PUT = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { paymentId, refundAmount, reason } = body;
    
    if (!paymentId || !refundAmount) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'paymentId and refundAmount are required',
        },
        { status: 400 }
      );
    }
    
    const payment = await Payment.findById(paymentId);
    
    if (!payment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Payment not found',
        },
        { status: 404 }
      );
    }
    
    if (payment.status !== 'succeeded') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Status',
          message: 'Can only refund successful payments',
        },
        { status: 409 }
      );
    }
    
    // Process refund through Stripe
    const refund = await stripe.refunds.create({
      payment_intent: payment.stripe.paymentIntentId,
      amount: Math.round(refundAmount * 100), // Convert to cents
      reason: 'requested_by_customer',
      metadata: {
        reason: reason || 'Refund requested',
      },
    });
    
    // Update payment record
    await payment.processRefund(refundAmount, reason);
    
    // Update booking payment status
    const booking = await Booking.findById(payment.booking);
    if (booking) {
      const newPaidAmount = booking.payment.paidAmount - refundAmount;
      await booking.updatePaymentStatus(
        newPaidAmount <= 0 ? 'refunded' : 'partial',
        -refundAmount
      );
    }
    
    return NextResponse.json({
      success: true,
      data: {
        payment,
        refund: {
          id: refund.id,
          amount: refund.amount / 100,
          status: refund.status,
        },
      },
      message: 'Refund processed successfully',
    });
  } catch (error) {
    console.error('Error processing refund:', error);
    
    if (error.type?.startsWith('Stripe')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Stripe Error',
          message: error.message,
          code: error.code,
        },
        { status: 402 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process refund',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
