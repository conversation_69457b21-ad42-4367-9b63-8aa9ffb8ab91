import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import Link from 'next/link';

export default async function DashboardPage() {
  const session = await auth();
  
  if (!session) {
    redirect('/auth/signin?callbackUrl=/dashboard');
  }
  
  // Redirect admins to admin dashboard
  if (session.user.role === 'admin') {
    redirect('/admin/dashboard');
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Welcome to Your Dashboard
          </h1>
          <p className="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
            Hello, {session.user.name || session.user.email}
          </p>
        </div>
        
        <div className="mt-10 grid gap-5 sm:grid-cols-2 lg:grid-cols-3">
          <Link 
            href="/packages"
            className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-300"
          >
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">View Packages</h3>
              <p className="mt-2 text-sm text-gray-500">
                Browse our available lodge packages and pricing options.
              </p>
            </div>
          </Link>
          
          <Link 
            href="/bookings"
            className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-300"
          >
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">My Bookings</h3>
              <p className="mt-2 text-sm text-gray-500">
                View and manage your current and past bookings.
              </p>
            </div>
          </Link>
          
          <Link 
            href="/profile"
            className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-300"
          >
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Profile Settings</h3>
              <p className="mt-2 text-sm text-gray-500">
                Update your personal information and preferences.
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}