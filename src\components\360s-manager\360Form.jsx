'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdImage, MdInfo } from 'react-icons/md';

export default function ThreeSixtyForm({ 
  threeSixty = null, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    originalFileName: '',
    priority: 0,
  });
  const [errors, setErrors] = useState({});
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [uploading, setUploading] = useState(false);

  // Initialize form data when threeSixty prop changes
  useEffect(() => {
    if (threeSixty) {
      setFormData({
        name: threeSixty.name || '',
        url: threeSixty.url || '',
        originalFileName: threeSixty.originalFileName || '',
        priority: threeSixty.priority || 0,
      });
      setImagePreview(threeSixty.url || '');
    } else {
      setFormData({
        name: '',
        url: '',
        originalFileName: '',
        priority: 0,
      });
      setImagePreview('');
    }
  }, [threeSixty]);

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      if (files.length === 1) {
        // Single file - existing behavior
        const file = files[0];
        setImageFile(file);
        setFormData(prev => ({
          ...prev,
          originalFileName: file.name
        }));

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Multiple files - trigger multi-upload
        handleMultipleUpload(files);
      }
    }
  };

  const handleMultipleUpload = async (files) => {
    setUploading(true);
    const uploadResults = [];
    const errors = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        try {
          // Validate file
          if (!file.type.startsWith('image/')) {
            errors.push(`${file.name}: Not a valid image file`);
            continue;
          }

          if (file.size > 10 * 1024 * 1024) { // 10MB limit
            errors.push(`${file.name}: File size exceeds 10MB limit`);
            continue;
          }

          // Upload individual file
          const formData = new FormData();
          formData.append('files', file);

          const response = await fetch('/api/upload/360s', {
            method: 'POST',
            body: formData,
          });

          const result = await response.json();

          if (result.success && result.data.length > 0) {
            // Create 360° record for each uploaded file
            const threeSixtyData = {
              name: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
              url: result.data[0].url,
              originalFileName: file.name,
              priority: 0,
              description: '',
            };

            const saveResponse = await fetch('/api/360s', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(threeSixtyData),
            });

            const saveResult = await saveResponse.json();

            if (saveResult.success) {
              uploadResults.push({
                file: file.name,
                success: true,
                data: saveResult.data
              });
            } else {
              errors.push(`${file.name}: Failed to save - ${saveResult.message}`);
            }
          } else {
            errors.push(`${file.name}: Upload failed`);
          }
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error);
          errors.push(`${file.name}: ${error.message}`);
        }
      }

      // Show results
      if (uploadResults.length > 0) {
        alert(`Successfully uploaded ${uploadResults.length} files${errors.length > 0 ? ` (${errors.length} failed)` : ''}`);
        // Trigger refresh of the list
        if (onSave) {
          window.location.reload(); // Simple refresh for now
        }
      }

      if (errors.length > 0) {
        console.error('Upload errors:', errors);
        alert(`Upload errors:\n${errors.join('\n')}`);
      }

    } catch (error) {
      console.error('Multi-upload error:', error);
      alert('Failed to upload files. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const uploadImage = async () => {
    if (!imageFile) return null;
    
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('files', imageFile);
      
      const response = await fetch('/api/upload/360s', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (result.success && result.data.length > 0) {
        return result.data[0].url;
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.url && !imageFile) {
      newErrors.url = 'Image is required';
    }
    
    if (formData.priority < 0) {
      newErrors.priority = 'Priority must be 0 or greater';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      let imageUrl = formData.url;
      
      // Upload new image if selected
      if (imageFile) {
        imageUrl = await uploadImage();
      }
      
      const submitData = {
        ...formData,
        url: imageUrl,
      };
      
      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save 360 image. Please try again.' });
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {threeSixty ? 'Edit 360° Image' : 'Create New 360° Image'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter 360° image name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Priority */}
        <div>
          <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
            Priority
          </label>
          <input
            type="number"
            id="priority"
            name="priority"
            value={formData.priority}
            onChange={handleInputChange}
            min="0"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.priority ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter priority (0 = highest)"
          />
          {errors.priority && (
            <p className="mt-1 text-sm text-red-600">{errors.priority}</p>
          )}
          <p className="mt-1 text-sm text-gray-500">
            Lower numbers have higher priority (0 = highest priority)
          </p>
        </div>

        {/* Original File Name (read-only if editing) */}
        {formData.originalFileName && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Original File Name
            </label>
            <input
              type="text"
              value={formData.originalFileName}
              readOnly
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
            />
          </div>
        )}

        {/* Image Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            360° Image *
          </label>
          
          {/* Image Preview */}
          {imagePreview && (
            <div className="mb-4">
              <img
                src={imagePreview}
                alt="Preview"
                className="w-64 h-32 object-cover rounded-md border border-gray-300"
              />
            </div>
          )}
          
          {/* File Input */}
          <div className="space-y-3">
            <div className="flex items-center space-x-4">
              <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
                <MdCloudUpload className="mr-2" />
                Choose 360° Image(s)
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageChange}
                  className="hidden"
                />
              </label>

              {imageFile && (
                <span className="text-sm text-gray-600">
                  {imageFile.name}
                </span>
              )}
            </div>

            {/* Multi-upload info */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <div className="flex items-start space-x-2">
                <MdInfo className="text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">Multi-Upload Support:</p>
                  <ul className="mt-1 space-y-1 text-xs">
                    <li>• Select multiple files to upload them all at once</li>
                    <li>• Each file will be created as a separate 360° image</li>
                    <li>• Files will be automatically named based on filename</li>
                    <li>• All files will have priority 0 (can be edited later)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {errors.url && (
            <p className="mt-1 text-sm text-red-600">{errors.url}</p>
          )}

          <p className="mt-1 text-sm text-gray-500">
            Upload 360° panoramic image(s). Recommended format: equirectangular projection. Max 10MB per file.
          </p>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {isLoading || uploading ? 'Saving...' : 'Save 360° Image'}
          </button>
        </div>
      </form>
    </div>
  );
}
