'use client';

import { useState, useEffect, useRef, Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { MdArrowBack, MdFullscreen, MdFullscreenExit, MdHelp, MdNavigateBefore, MdNavigateNext } from 'react-icons/md';
import { useRouter } from 'next/navigation';
import PanoramicSphere from './PanoramicSphere';
import ThumbnailPanel from './ThumbnailPanel';
import LoadingOverlay from './LoadingOverlay';
import FadeTransition from './FadeTransition';
import TextureStatusIndicator from './TextureStatusIndicator';
import MarkerInputsPanel from './MarkerInputsPanel';

export default function ThreeSixtyViewer() {
  const router = useRouter();
  const [threeSixties, setThreeSixties] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [textureCache, setTextureCache] = useState(new Map());
  const [loadingQueue, setLoadingQueue] = useState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);

  // Fetch 360° images from API
  useEffect(() => {
    fetchThreeSixties();
  }, []);

  // Handle fullscreen changes and keyboard controls
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (currentImageIndex > 0) {
            handleImageChange(currentImageIndex - 1);
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (currentImageIndex < threeSixties.length - 1) {
            handleImageChange(currentImageIndex + 1);
          }
          break;
        case 'f':
        case 'F':
          event.preventDefault();
          toggleFullscreen();
          break;
        case 'Escape':
          if (document.fullscreenElement) {
            document.exitFullscreen();
          }
          break;
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentImageIndex, threeSixties.length]);

  const fetchThreeSixties = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch with priority-based sorting (descending order: 10, 9, 8, etc.)
      const response = await fetch('/api/360s?sort=priority&order=desc&limit=50');
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        // Sort by priority to ensure lowest priority (highest number) comes first
        const sortedData = data.data.sort((a, b) => (b.priority || 0) - (a.priority || 0));
        setThreeSixties(sortedData);

        // Start with lowest priority image (index 0)
        setCurrentImageIndex(0);

        // Start loading textures with priority-based loading
        initializeTextureLoading(sortedData);
      } else {
        setError('No 360° images found');
      }
    } catch (err) {
      console.error('Error fetching 360° images:', err);
      setError('Failed to load 360° images');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeTextureLoading = (images) => {
    // Sort by priority for loading order (highest number = lowest priority)
    const sortedImages = [...images].sort((a, b) => (b.priority || 0) - (a.priority || 0));

    // Set loading queue with priority-based ordering
    setLoadingQueue(sortedImages.map((img, index) => ({
      ...img,
      originalIndex: images.findIndex(item => item._id === img._id),
      priority: img.priority || 0,
      loadOrder: index,
      status: 'pending' // pending, downloading, cached, ready
    })));
  };

  const handleImageChange = async (index) => {
    if (index === currentImageIndex || isTransitioning) return;
    
    setIsTransitioning(true);
    
    // Wait for fade out
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setCurrentImageIndex(index);
    
    // Wait for fade in
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setIsTransitioning(false);
  };

  const toggleFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (!document.fullscreenElement) {
        await containerRef.current.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (err) {
      console.error('Fullscreen error:', err);
    }
  };

  const handleBack = () => {
    router.push('/admin/360s-manager/file-manager');
  };

  if (isLoading) {
    return <LoadingOverlay message="Loading 360° images..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Error Loading 360° Viewer</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  if (threeSixties.length === 0) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No 360° Images Available</h2>
          <p className="text-gray-300 mb-6">Upload some 360° images to get started.</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  const currentImage = threeSixties[currentImageIndex];

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-screen bg-black overflow-hidden"
    >
      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 0.1], fov: 75 }}
        className="w-full h-full"
      >
        <Suspense fallback={null}>
          <PanoramicSphere
            imageUrl={currentImage?.url}
            imageId={currentImage?._id}
            textureCache={textureCache}
            setTextureCache={setTextureCache}
            loadingQueue={loadingQueue}
            setLoadingQueue={setLoadingQueue}
            onTextureLoad={() => {
              // Texture loaded successfully
            }}
          />
        </Suspense>
      </Canvas>

      {/* Fade Transition Overlay */}
      <FadeTransition isTransitioning={isTransitioning} />

    </div>
  );
}
