'use client';

import { useState, useEffect, useRef, Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { MdArrowBack, MdFullscreen, MdFullscreenExit, MdHelp, MdNavigateBefore, MdNavigateNext } from 'react-icons/md';
import { useRouter } from 'next/navigation';
import PanoramicSphere from './PanoramicSphere';
import ThumbnailPanel from './ThumbnailPanel';
import LoadingOverlay from './LoadingOverlay';
import FadeTransition from './FadeTransition';

export default function ThreeSixtyViewer() {
  const router = useRouter();
  const [threeSixties, setThreeSixties] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [textureCache, setTextureCache] = useState(new Map());
  const [loadingQueue, setLoadingQueue] = useState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);

  // Fetch 360° images from API
  useEffect(() => {
    fetchThreeSixties();
  }, []);

  // Handle fullscreen changes and keyboard controls
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (currentImageIndex > 0) {
            handleImageChange(currentImageIndex - 1);
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (currentImageIndex < threeSixties.length - 1) {
            handleImageChange(currentImageIndex + 1);
          }
          break;
        case 'f':
        case 'F':
          event.preventDefault();
          toggleFullscreen();
          break;
        case 'Escape':
          if (document.fullscreenElement) {
            document.exitFullscreen();
          }
          break;
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentImageIndex, threeSixties.length]);

  const fetchThreeSixties = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/360s?sort=priority&limit=50');
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        setThreeSixties(data.data);
        // Start loading textures with priority-based loading
        initializeTextureLoading(data.data);
      } else {
        setError('No 360° images found');
      }
    } catch (err) {
      console.error('Error fetching 360° images:', err);
      setError('Failed to load 360° images');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeTextureLoading = (images) => {
    // Sort by priority (highest first) for loading order
    const sortedImages = [...images].sort((a, b) => (b.priority || 0) - (a.priority || 0));
    
    // Set loading queue
    setLoadingQueue(sortedImages.map((img, index) => ({
      ...img,
      originalIndex: images.findIndex(item => item._id === img._id),
      priority: img.priority || 0,
      loadOrder: index
    })));
  };

  const handleImageChange = async (index) => {
    if (index === currentImageIndex || isTransitioning) return;
    
    setIsTransitioning(true);
    
    // Wait for fade out
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setCurrentImageIndex(index);
    
    // Wait for fade in
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setIsTransitioning(false);
  };

  const toggleFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (!document.fullscreenElement) {
        await containerRef.current.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (err) {
      console.error('Fullscreen error:', err);
    }
  };

  const handleBack = () => {
    router.push('/admin/360s-manager/file-manager');
  };

  if (isLoading) {
    return <LoadingOverlay message="Loading 360° images..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Error Loading 360° Viewer</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  if (threeSixties.length === 0) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No 360° Images Available</h2>
          <p className="text-gray-300 mb-6">Upload some 360° images to get started.</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  const currentImage = threeSixties[currentImageIndex];

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-screen bg-black overflow-hidden"
    >
      {/* Top Controls */}
      <div className="absolute top-4 left-4 right-4 z-20 flex justify-between items-center">
        <button
          onClick={handleBack}
          className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-lg transition-colors backdrop-blur-sm"
        >
          <MdArrowBack size={24} />
        </button>
        
        <div className="text-center text-white">
          <h1 className="text-xl font-bold">{currentImage?.name || 'Untitled'}</h1>
          <p className="text-sm text-gray-300">
            {currentImageIndex + 1} of {threeSixties.length}
          </p>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setShowHelp(!showHelp)}
            className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-lg transition-colors backdrop-blur-sm"
          >
            <MdHelp size={24} />
          </button>

          <button
            onClick={toggleFullscreen}
            className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-lg transition-colors backdrop-blur-sm"
          >
            {isFullscreen ? <MdFullscreenExit size={24} /> : <MdFullscreen size={24} />}
          </button>
        </div>
      </div>

      {/* Navigation Arrows */}
      {threeSixties.length > 1 && (
        <>
          <button
            onClick={() => handleImageChange(currentImageIndex - 1)}
            disabled={currentImageIndex === 0 || isTransitioning}
            className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-4 rounded-full transition-all backdrop-blur-sm ${
              currentImageIndex === 0 || isTransitioning
                ? 'opacity-30 cursor-not-allowed'
                : 'hover:scale-110'
            }`}
          >
            <MdNavigateBefore size={32} />
          </button>

          <button
            onClick={() => handleImageChange(currentImageIndex + 1)}
            disabled={currentImageIndex === threeSixties.length - 1 || isTransitioning}
            className={`absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-4 rounded-full transition-all backdrop-blur-sm ${
              currentImageIndex === threeSixties.length - 1 || isTransitioning
                ? 'opacity-30 cursor-not-allowed'
                : 'hover:scale-110'
            }`}
          >
            <MdNavigateNext size={32} />
          </button>
        </>
      )}

      {/* Thumbnail Panel */}
      <ThumbnailPanel
        images={threeSixties}
        currentIndex={currentImageIndex}
        onImageSelect={handleImageChange}
        isTransitioning={isTransitioning}
      />

      {/* Help Overlay */}
      {showHelp && (
        <div className="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-40">
          <div className="bg-black/90 text-white p-8 rounded-lg max-w-md mx-4 border border-white/20">
            <h3 className="text-xl font-bold mb-4">360° Viewer Controls</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span>Look around:</span>
                <span className="text-gray-300">Click & drag / Touch & drag</span>
              </div>
              <div className="flex justify-between">
                <span>Previous image:</span>
                <span className="text-gray-300">← Arrow key / Left button</span>
              </div>
              <div className="flex justify-between">
                <span>Next image:</span>
                <span className="text-gray-300">→ Arrow key / Right button</span>
              </div>
              <div className="flex justify-between">
                <span>Fullscreen:</span>
                <span className="text-gray-300">F key / Fullscreen button</span>
              </div>
              <div className="flex justify-between">
                <span>Exit fullscreen:</span>
                <span className="text-gray-300">Escape key</span>
              </div>
              <div className="flex justify-between">
                <span>Select image:</span>
                <span className="text-gray-300">Click thumbnail</span>
              </div>
            </div>
            <button
              onClick={() => setShowHelp(false)}
              className="mt-6 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Got it!
            </button>
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 0], fov: 75 }}
        className="w-full h-full"
      >
        <Suspense fallback={null}>
          <PanoramicSphere
            imageUrl={currentImage?.url}
            imageId={currentImage?._id}
            textureCache={textureCache}
            setTextureCache={setTextureCache}
            loadingQueue={loadingQueue}
            setLoadingQueue={setLoadingQueue}
            onTextureLoad={() => {
              // Texture loaded successfully
            }}
          />
        </Suspense>
      </Canvas>

      {/* Fade Transition Overlay */}
      <FadeTransition isTransitioning={isTransitioning} />

      {/* Loading Overlay for texture loading */}
      {isLoading && (
        <LoadingOverlay message="Loading textures..." />
      )}
    </div>
  );
}
