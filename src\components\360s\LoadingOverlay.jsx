'use client';

export default function LoadingOverlay({ message = 'Loading...', progress = null }) {
  return (
    <div className="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center text-white">
        {/* Loading Spinner */}
        <div className="relative mb-6">
          <div className="w-16 h-16 border-4 border-white/20 rounded-full"></div>
          <div className="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-white rounded-full animate-spin"></div>
        </div>

        {/* Loading Message */}
        <h2 className="text-xl font-semibold mb-2">{message}</h2>
        
        {/* Progress Bar (if progress is provided) */}
        {progress !== null && (
          <div className="w-64 mx-auto">
            <div className="bg-white/20 rounded-full h-2 mb-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
              />
            </div>
            <p className="text-sm text-white/70">
              {Math.round(progress)}% complete
            </p>
          </div>
        )}

        {/* Loading Tips */}
        <div className="mt-6 text-sm text-white/60 max-w-md">
          <p>Loading high-resolution 360° panoramic images...</p>
          <p className="mt-1">This may take a moment depending on your connection.</p>
        </div>
      </div>
    </div>
  );
}
