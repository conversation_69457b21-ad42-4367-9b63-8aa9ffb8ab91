'use client';

import { useState, useRef, useEffect,useContext } from 'react';
import { MdChevronLeft, MdChevronRight, MdImage } from 'react-icons/md';
import MarkersInputList from './MarkersInputList';
// import {ThreeSixtyContext} from './ThreeSixtyContext'

export default function MarkerInputsPanel({
  textureData,
}) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [markerList, setMarkerList] = useState([]);
  const [_360Object, set_360Object] = useState({});
  // const {markerList,setMarkerList,_360Object,set_360Object}=useContext(ThreeSixtyContext)

  const togglePanel = () => {
    setIsExpanded(!isExpanded);
  };

  console.log('MarkerInputsPanel textureData:', textureData);

  return (
    <div className={`absolute right-4 top-20 bottom-4 z-10 transition-all duration-300 ${
      isExpanded ? 'w-80' : 'w-12'
    }`}>
      <div className="h-full bg-black/70 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden">
        {/* Header */}
        <div className="p-3 border-b border-white/10 flex items-center justify-between">
          <button
            onClick={togglePanel}
            className="text-white/70 hover:text-white transition-colors p-1"
          >
            {!isExpanded ? <MdChevronLeft size={20} /> : <MdChevronRight size={20} />}
          </button>
          {isExpanded && (
            <h3 className="text-white font-medium text-sm">
              360° Marker Inputs
            </h3>
          )}
        </div>

        {/* Marker Input State */} 
        <MarkersInputList
          textureData={textureData}
          markerList={markerList}
          _360Object={_360Object}
          set_360Object={set_360Object}
          setMarkerList={setMarkerList}
        />

        {/* Collapsed State */}
        {!isExpanded && (
          <div className="p-2 flex flex-col items-center space-y-2">
            <div className="w-1 h-8 bg-white/20 rounded-full relative">
              <div 
                className="w-1 bg-blue-400 rounded-full transition-all duration-300"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
