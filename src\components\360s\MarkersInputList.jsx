'use client';

import { settings } from '@/lib/settings';
import React, { useEffect, useState } from 'react'
import { BiMenuAltRight, BiTrash } from 'react-icons/bi'
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa';

const css='flex items-center w-full h-8 bg-gray-50 rounded shadow placeholder:text-xs text-xs placeholder:text-gray-600 text-gray-600 px-2 outline-none'

export default function MarkersInputList({textureData,markerList,_360Object,set_360Object,setMarkerList}) {
  const [isCollapsed, setIsCollapsed] = useState(true)
  const [input, setInput] = useState({})

  const handleAddList = () => {
    if(input?.name && input?.markerType){
      setMarkerList([...markerList,input])
    }
  }

  useEffect(() => {
    setMarkerList(_360Object?.markerList)
  }, [_360Object?.markerList])

  const handleSubmit = () => {
    try {
      fetch('/api/360s', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify([{..._360Object,markerList:markerList}]),
      })
    } catch (error) {
      console.log(error)
    }
  }
  
  // Uncomment for debugging
  console.log('MarkersInputList markerList:', markerList)
  console.log('MarkersInputList _360Object:', _360Object)

  return (
    <div className={`flex z-10 absolute right-0 items-center h-full duration-300 ease-linear flex-col bg-gray-50 w-full`}>
      <BiMenuAltRight className='text-2xl h-10'/>
      <hr className='w-full border-1 border-gray-300'/>

      <span className={`${isCollapsed ? 'hidden' : 'block'} font-medium text-sm flex-grow py-2 mx-2`}>Marker Settings</span>

      <div className='relative w-full h-[calc(100%-80px)] flex'>
        <div className='inputWrapper w-full px-2'>
          <div className='w-full h-fit flex flex-col bg-gray-100 rounded-md shadow p-2 border-1 border-gray-200'>
            <span className='text-sm text-gray-600 font-medium'>Marker title</span>
            <div className='flex items-center h-fit gap-1 p-1 shadow-md rounded mt-2 bg-gray-300'>
              <input onChange={e=>setInput({...input,name:e.target.value})} className={css} type="text" placeholder='Marker Name'/>
              <select onChange={e=>setInput({...input,markerType:e.target.value})} className={`${css}`} name="" id="">
                <option value="">Marker Type</option>
                {settings.markerList.markerType.map((i,index)=>(
                  <option key={index} value={i}>{i}</option>
                ))}
              </select>
              <button onClick={()=>handleAddList()} className='w-fit px-2 capitalize h-9 rounded text-xs justify-center items-center cursor-pointer bg-gray-800 text-white shadow'>Add</button>
            </div>
          </div>
          <hr className='w-full border-1 border-gray-300 my-2'/>
          <div className='w-full h-[calc(100%-128px)] flex-col flex items-center gap-2 bg-gray-50 mt-2 rounded-md shadow p-2 border-1 border-gray-200'>
            <div className='flex flex-col gap-2 w-full overflow-y-auto h-full'>
              {markerList?.map((i,index)=>(
                <div key={index} className='flex w-full h-fit flex-col p-2 bg-gray-200 rounded items-center gap-1'>
                  {/* Marker data */}
                  <div className='flex w-full h-7 items-center gap-2'>
                    <div className='flex w-full items-center gap-6'>
                      <span className='text-xs capitalize text-gray-600'>name:</span>
                      <span className='text-xs capitalize text-gray-600 font-medium'>{i?.name}</span>
                    </div>
                    <BiTrash onClick={()=>setMarkerList(markerList.filter((item)=>item?.name!==i?.name))} className='text-2xl cursor-pointer text-gray-400 border-1 border-gray-400 rounded h-2/3 w-fit'/>
                  </div>
                  {(i?.markerType===settings.markerList.markerType[0] || i?.markerType===settings.markerList.markerType[1] || i?.markerType===settings.markerList.markerType[2] || i?.markerType===settings.markerList.markerType[3])
                    ? <select defaultValue={i?._360Name ? i?._360Name : ''} onChange={e=>setMarkerList(markerList.map((item)=>item?.name===i?.name ? {...item,_360Name:e.target.value} : item))} className={`${css}`} name="" id="">
                        <option value="">360 Name</option>
                        {textureData?.map((i,index)=>(
                          <option key={index} value={i?.name}>{i?.name}</option>
                        ))}
                      </select>
                    : <select onChange={e=>setMarkerList(markerList.map((item)=>item?.name===i?.name ? {...item,infoType:e.target.value} : item))} className={`${css}`} name="" id="">
                      <option value="">Content Type</option>
                        {settings.markerList.contentType.map((i,index)=>(
                          <option key={index} value={i}>{i}</option>
                        ))}
                      </select>
                  }
                </div>
              ))}
            </div>
            <button onClick={handleSubmit} className='flex h-10 w-full max-h-10 px-2 capitalize rounded text-xs justify-center items-center cursor-pointer bg-gray-800 text-white shadow'>submit</button>
          </div>
        </div>
      </div>
    </div>
  )
}
