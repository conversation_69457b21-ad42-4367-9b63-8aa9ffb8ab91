'use client';

import { useRef, useEffect, useState } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import * as THREE from 'three';

export default function PanoramicSphere({
  imageUrl,
  imageId,
  textureCache,
  setTextureCache,
  loadingQueue,
  setLoadingQueue,
  onTextureLoad
}) {
  const meshRef = useRef();
  const { camera, gl } = useThree();
  const [currentTexture, setCurrentTexture] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const mouseRef = useRef({ x: 0, y: 0 });
  const isMouseDown = useRef(false);
  const rotationRef = useRef({ x: 0, y: 0 });

  // Mouse/touch controls
  useEffect(() => {
    const canvas = gl.domElement;
    
    const handleMouseDown = (event) => {
      isMouseDown.current = true;
      mouseRef.current = {
        x: event.clientX || event.touches?.[0]?.clientX || 0,
        y: event.clientY || event.touches?.[0]?.clientY || 0
      };
    };

    const handleMouseMove = (event) => {
      if (!isMouseDown.current) return;

      const currentX = event.clientX || event.touches?.[0]?.clientX || 0;
      const currentY = event.clientY || event.touches?.[0]?.clientY || 0;

      const deltaX = currentX - mouseRef.current.x;
      const deltaY = currentY - mouseRef.current.y;

      rotationRef.current.y += deltaX * 0.005;
      rotationRef.current.x += deltaY * 0.005;

      // Limit vertical rotation
      rotationRef.current.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, rotationRef.current.x));

      mouseRef.current = { x: currentX, y: currentY };
    };

    const handleMouseUp = () => {
      isMouseDown.current = false;
    };

    // Mouse events
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseUp);

    // Touch events
    canvas.addEventListener('touchstart', handleMouseDown, { passive: false });
    canvas.addEventListener('touchmove', handleMouseMove, { passive: false });
    canvas.addEventListener('touchend', handleMouseUp);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseUp);
      canvas.removeEventListener('touchstart', handleMouseDown);
      canvas.removeEventListener('touchmove', handleMouseMove);
      canvas.removeEventListener('touchend', handleMouseUp);
    };
  }, [gl]);

  // Load texture asynchronously
  const loadTexture = async (url, id) => {
    if (textureCache.has(id)) {
      return textureCache.get(id);
    }

    setIsLoading(true);
    
    try {
      const loader = new THREE.TextureLoader();
      const texture = await new Promise((resolve, reject) => {
        loader.load(
          url,
          (loadedTexture) => {
            // Configure texture for panoramic use
            loadedTexture.mapping = THREE.EquirectangularReflectionMapping;
            loadedTexture.wrapS = THREE.RepeatWrapping;
            loadedTexture.wrapT = THREE.ClampToEdgeWrapping;
            loadedTexture.minFilter = THREE.LinearFilter;
            loadedTexture.magFilter = THREE.LinearFilter;
            loadedTexture.flipY = false;
            resolve(loadedTexture);
          },
          undefined,
          reject
        );
      });

      // Cache the texture
      const newCache = new Map(textureCache);
      newCache.set(id, texture);
      setTextureCache(newCache);

      return texture;
    } catch (error) {
      console.error('Error loading texture:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Background texture loading for queue
  useEffect(() => {
    if (loadingQueue.length === 0) return;

    const loadNextTexture = async () => {
      const nextItem = loadingQueue[0];
      if (!nextItem || textureCache.has(nextItem._id)) {
        // Remove from queue if already cached
        setLoadingQueue(prev => prev.slice(1));
        return;
      }

      try {
        await loadTexture(nextItem.url, nextItem._id);
        // Remove from queue after successful load
        setLoadingQueue(prev => prev.slice(1));
      } catch (error) {
        console.error('Background texture loading failed:', error);
        // Still remove from queue to prevent infinite retry
        setLoadingQueue(prev => prev.slice(1));
      }
    };

    // Small delay to prevent blocking the main thread
    const timeoutId = setTimeout(loadNextTexture, 100);
    return () => clearTimeout(timeoutId);
  }, [loadingQueue, textureCache, setTextureCache, setLoadingQueue]);

  // Load current texture when imageUrl changes
  useEffect(() => {
    if (!imageUrl || !imageId) return;

    const loadCurrentTexture = async () => {
      const texture = await loadTexture(imageUrl, imageId);
      if (texture) {
        setCurrentTexture(texture);
        onTextureLoad?.();
      }
    };

    loadCurrentTexture();
  }, [imageUrl, imageId]);

  // Update camera rotation
  useFrame(() => {
    if (camera) {
      camera.rotation.x = rotationRef.current.x;
      camera.rotation.y = rotationRef.current.y;
    }
  });

  if (!currentTexture) {
    return null;
  }

  return (
    <mesh ref={meshRef} scale={[-1, 1, 1]}>
      <sphereGeometry args={[500, 60, 40]} />
      <meshBasicMaterial 
        map={currentTexture} 
        side={THREE.BackSide}
        transparent={false}
      />
    </mesh>
  );
}
