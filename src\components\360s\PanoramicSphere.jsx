'use client';

import { useRef, useEffect, useState } from 'react';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

export default function PanoramicSphere({
  imageUrl,
  imageId,
  textureCache,
  setTextureCache,
  loadingQueue,
  setLoadingQueue,
  onTextureLoad
}) {
  const meshRef = useRef();
  const controlsRef = useRef();
  const [currentTexture, setCurrentTexture] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load texture asynchronously
  const loadTexture = async (url, id) => {
    if (textureCache.has(id)) {
      return textureCache.get(id);
    }

    setIsLoading(true);
    
    try {
      const loader = new THREE.TextureLoader();
      const texture = await new Promise((resolve, reject) => {
        loader.load(
          url,
          (loadedTexture) => {
            // Configure texture for panoramic use
            loadedTexture.mapping = THREE.EquirectangularReflectionMapping;
            loadedTexture.wrapS = THREE.RepeatWrapping;
            loadedTexture.wrapT = THREE.ClampToEdgeWrapping;
            loadedTexture.minFilter = THREE.LinearFilter;
            loadedTexture.magFilter = THREE.LinearFilter;
            loadedTexture.flipY = true;
            resolve(loadedTexture);
          },
          undefined,
          reject
        );
      });

      // Cache the texture
      const newCache = new Map(textureCache);
      newCache.set(id, texture);
      setTextureCache(newCache);

      return texture;
    } catch (error) {
      console.error('Error loading texture:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Background texture loading for queue
  useEffect(() => {
    if (loadingQueue.length === 0) return;

    const loadNextTexture = async () => {
      const nextItem = loadingQueue[0];
      if (!nextItem || textureCache.has(nextItem._id)) {
        // Remove from queue if already cached
        setLoadingQueue(prev => prev.slice(1));
        return;
      }

      try {
        await loadTexture(nextItem.url, nextItem._id);
        // Remove from queue after successful load
        setLoadingQueue(prev => prev.slice(1));
      } catch (error) {
        console.error('Background texture loading failed:', error);
        // Still remove from queue to prevent infinite retry
        setLoadingQueue(prev => prev.slice(1));
      }
    };

    // Small delay to prevent blocking the main thread
    const timeoutId = setTimeout(loadNextTexture, 100);
    return () => clearTimeout(timeoutId);
  }, [loadingQueue, textureCache, setTextureCache, setLoadingQueue]);

  // Load current texture when imageUrl changes
  useEffect(() => {
    if (!imageUrl || !imageId) return;

    const loadCurrentTexture = async () => {
      const texture = await loadTexture(imageUrl, imageId);
      if (texture) {
        setCurrentTexture(texture);
        onTextureLoad?.();
      }
    };

    loadCurrentTexture();
  }, [imageUrl, imageId]);

  if (!currentTexture) {
    return (
      <>
        <OrbitControls
          ref={controlsRef}
          enableZoom={false}
          enablePan={false}
          enableRotate={true}
          enableDamping={true}
          dampingFactor={0.05}
          rotateSpeed={0.5}
          minPolarAngle={0}
          maxPolarAngle={Math.PI}
          minAzimuthAngle={-Infinity}
          maxAzimuthAngle={Infinity}
          target={[0, 0, 0]}
        />
      </>
    );
  }

  return (
    <>
      <OrbitControls
        ref={controlsRef}
        enableZoom={false}
        enablePan={false}
        enableRotate={true}
        enableDamping={true}
        dampingFactor={0.05}
        rotateSpeed={0.5}
        minPolarAngle={0}
        maxPolarAngle={Math.PI}
        minAzimuthAngle={-Infinity}
        maxAzimuthAngle={Infinity}
        target={[0, 0, 0]}
      />
      <mesh ref={meshRef} scale={[1, 1, 1]}>
        <sphereGeometry args={[500, 60, 40]} />
        <meshBasicMaterial
          map={currentTexture}
          side={THREE.BackSide}
          transparent={false}
        />
      </mesh>
    </>
  );
}
