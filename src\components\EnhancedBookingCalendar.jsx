'use client';

import React, { useState, useEffect, useMemo, memo } from 'react';
import { MdOutlineDateRange, MdChevronLeft, MdChevronRight } from 'react-icons/md';

function EnhancedBookingCalendar({
  onDateRangeChange,
  isLoading = false,
  minDate = new Date(),
  maxDate = null,
  className = '',
  selectedPackage = null
}) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [checkInDate, setCheckInDate] = useState(null);
  const [checkOutDate, setCheckOutDate] = useState(null);
  const [isSelectingCheckOut, setIsSelectingCheckOut] = useState(false);
  const [availabilityData, setAvailabilityData] = useState({});
  const [loadingAvailability, setLoadingAvailability] = useState(false);

  // Fetch availability data when month changes
  useEffect(() => {
    const fetchAvailability = async () => {
      setLoadingAvailability(true);
      try {
        const startDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        const endDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

        const response = await fetch(
          `/api/bookings/availability?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}`
        );

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            // Transform the API response to the expected format
            const transformedData = {};

            if (result.data.requestedDates) {
              result.data.requestedDates.forEach(dateInfo => {
                transformedData[dateInfo.date] = {
                  isAvailable: dateInfo.available,
                  conflictingBookings: dateInfo.booking ? [dateInfo.booking] : [],
                  isBlackout: false, // API doesn't provide blackout info yet
                  blackoutReason: null,
                };
              });
            }

            setAvailabilityData(transformedData);
          }
        } else {
          console.error('Availability API error:', response.status);
        }
      } catch (error) {
        console.error('Failed to fetch availability:', error);
      } finally {
        setLoadingAvailability(false);
      }
    };

    fetchAvailability();
  }, [currentMonth]);

  // Calculate number of nights when dates change
  useEffect(() => {
    if (checkInDate && checkOutDate) {
      const nights = Math.ceil((checkOutDate - checkInDate) / (1000 * 60 * 60 * 24));
      onDateRangeChange({
        checkIn: checkInDate,
        checkOut: checkOutDate,
        nights: nights
      });
    } else {
      onDateRangeChange({
        checkIn: null,
        checkOut: null,
        nights: 0
      });
    }
  }, [checkInDate, checkOutDate, onDateRangeChange]);

  // Generate calendar data
  const calendarData = useMemo(() => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
    
    const days = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return days;
  }, [currentMonth]);

  // Get availability status for a date
  const getDateAvailability = (date) => {
    const dateStr = date.toISOString().split('T')[0];
    const availability = availabilityData[dateStr];

    // Check if date is in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date < today) {
      return { status: 'past', message: 'Past date', selectable: false };
    }

    // Check date range limits
    if (minDate && date < minDate) {
      return { status: 'out-of-range', message: 'Before minimum date', selectable: false };
    }
    if (maxDate && date > maxDate) {
      return { status: 'out-of-range', message: 'After maximum date', selectable: false };
    }

    // Check if we have availability data
    if (!availability) {
      return { status: 'loading', message: 'Loading availability...', selectable: false };
    }

    // Check availability status
    if (!availability.isAvailable) {
      const reason = availability.conflictingBookings?.length > 0
        ? `Booked (${availability.conflictingBookings.length} booking${availability.conflictingBookings.length > 1 ? 's' : ''})`
        : 'Unavailable';
      return { status: 'unavailable', message: reason, selectable: false };
    }

    // Check if it's a blackout date
    if (availability.isBlackout) {
      return { status: 'blackout', message: availability.blackoutReason || 'Blackout date', selectable: false };
    }

    // Check capacity
    const bookingCount = availability.conflictingBookings?.length || 0;
    const maxCapacity = 3; // Assuming max 3 bookings per day

    if (bookingCount === 0) {
      return { status: 'available', message: 'Available', selectable: true };
    } else if (bookingCount < maxCapacity) {
      return { status: 'partial', message: `${bookingCount} booking${bookingCount > 1 ? 's' : ''} - Limited availability`, selectable: true };
    } else {
      return { status: 'full', message: 'Fully booked', selectable: false };
    }
  };



  // Check if a date is disabled
  const isDateDisabled = (date) => {
    const availability = getDateAvailability(date);
    return !availability.selectable;
  };

  // Check if a date is in the selected range
  const isInSelectedRange = (date) => {
    if (!checkInDate || !checkOutDate) return false;
    return date > checkInDate && date < checkOutDate;
  };

  // Handle date click
  const handleDateClick = (date) => {
    const availability = getDateAvailability(date);

    // Only allow selection of available dates
    if (!availability.selectable) return;

    if (!checkInDate || (checkInDate && checkOutDate)) {
      // Start new selection
      setCheckInDate(date);
      setCheckOutDate(null);
      setIsSelectingCheckOut(true);
    } else if (isSelectingCheckOut) {
      // Select check-out date
      if (date > checkInDate) {
        // Check if any dates in between are unavailable
        const currentDate = new Date(checkInDate);
        currentDate.setDate(currentDate.getDate() + 1);

        let canSelectRange = true;
        while (currentDate < date) {
          const dayAvailability = getDateAvailability(currentDate);
          if (!dayAvailability.selectable) {
            // Can't select this range, there's an unavailable date in between
            canSelectRange = false;
            break;
          }
          currentDate.setDate(currentDate.getDate() + 1);
        }

        if (canSelectRange) {
          setCheckOutDate(date);
          setIsSelectingCheckOut(false);
        } else {
          // Show visual feedback that range is not selectable
          // For now, just start a new selection
          setCheckInDate(date);
          setCheckOutDate(null);
        }
      } else {
        // Selected date is before check-in, start over
        setCheckInDate(date);
        setCheckOutDate(null);
      }
    }
  };

  // Navigate months
  const navigateMonth = (direction) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + direction);
    setCurrentMonth(newMonth);
  };

  // Handle navigation button clicks with preventDefault
  const handleNavigationClick = (e, direction) => {
    e.preventDefault();
    e.stopPropagation();
    navigateMonth(direction);
  };

  // Format month/year
  const formatMonthYear = (date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
    });
  };

  // Get date class names with availability states
  const getDateClassName = (date) => {
    const availability = getDateAvailability(date);
    let classes = 'relative w-full h-7 flex items-center justify-center text-sm rounded-md transition-all duration-200 ';

    // Selected dates take priority
    if (checkInDate && date.toDateString() === checkInDate.toDateString()) {
      classes += 'bg-blue-600 text-white font-semibold ring-2 ring-blue-300 ';
    } else if (checkOutDate && date.toDateString() === checkOutDate.toDateString()) {
      classes += 'bg-blue-600 text-white font-semibold ring-2 ring-blue-300 ';
    } else if (isInSelectedRange(date)) {
      classes += 'bg-blue-100 text-blue-800 ';
    } else {
      // Apply availability-based styling
      switch (availability.status) {
        case 'available':
          classes += date.getMonth() === currentMonth.getMonth()
            ? 'text-gray-900 bg-green-50 border border-green-200 hover:bg-green-100 cursor-pointer '
            : 'text-gray-400 bg-green-25 border border-green-100 hover:bg-green-50 cursor-pointer ';
          break;
        case 'partial':
          classes += date.getMonth() === currentMonth.getMonth()
            ? 'text-orange-800 bg-orange-50 border border-orange-200 hover:bg-orange-100 cursor-pointer '
            : 'text-orange-400 bg-orange-25 border border-orange-100 hover:bg-orange-50 cursor-pointer ';
          break;
        case 'unavailable':
        case 'full':
          classes += 'text-red-400 bg-red-50 border border-red-200 cursor-not-allowed ';
          break;
        case 'blackout':
          classes += 'text-gray-400 bg-gray-100 border border-gray-300 cursor-not-allowed ';
          break;
        case 'past':
        case 'out-of-range':
          classes += 'text-gray-300 bg-gray-50 cursor-not-allowed ';
          break;
        case 'loading':
          classes += 'text-gray-400 bg-gray-100 animate-pulse cursor-wait ';
          break;
        default:
          classes += date.getMonth() === currentMonth.getMonth()
            ? 'text-gray-900 hover:bg-gray-50 cursor-pointer '
            : 'text-gray-400 hover:text-gray-600 cursor-pointer ';
      }
    }

    return classes;
  };

  // Get availability indicator dot
  const getAvailabilityIndicator = (date) => {
    const availability = getDateAvailability(date);

    // Don't show indicator for selected dates or out of current month
    if ((checkInDate && date.toDateString() === checkInDate.toDateString()) ||
        (checkOutDate && date.toDateString() === checkOutDate.toDateString()) ||
        date.getMonth() !== currentMonth.getMonth()) {
      return null;
    }

    let dotColor = '';
    switch (availability.status) {
      case 'available':
        dotColor = 'bg-green-500';
        break;
      case 'partial':
        dotColor = 'bg-orange-500';
        break;
      case 'unavailable':
      case 'full':
        dotColor = 'bg-red-500';
        break;
      case 'blackout':
        dotColor = 'bg-gray-500';
        break;
      case 'loading':
        dotColor = 'bg-gray-400 animate-pulse';
        break;
      default:
        return null;
    }

    return (
      <div className={`absolute top-1 right-1 w-2 h-2 rounded-full ${dotColor}`}></div>
    );
  };

  // Clear selection
  const clearSelection = () => {
    setCheckInDate(null);
    setCheckOutDate(null);
    setIsSelectingCheckOut(false);
  };

  // Handle clear button click with preventDefault
  const handleClearClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    clearSelection();
  };

  // Handle date button click with preventDefault
  const handleDateButtonClick = (e, date) => {
    e.preventDefault();
    e.stopPropagation();
    handleDateClick(date);
  };

  if (isLoading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-2 text-sm ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-7 gap-2">
            {[...Array(35)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-2 text-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold text-gray-900 flex items-center">
          <MdOutlineDateRange className="mr-2" />
          Select Dates
        </h3>
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={(e) => handleNavigationClick(e, -1)}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <MdChevronLeft className="w-5 h-5" />
          </button>
          <span className="text-sm font-medium text-gray-700 min-w-32 text-center">
            {formatMonthYear(currentMonth)}
          </span>
          <button
            type="button"
            onClick={(e) => handleNavigationClick(e, 1)}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <MdChevronRight className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Selected dates display */}
      {(checkInDate || checkOutDate) && (
        <div className="mb-1 p-1 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="text-sm">
              <span className="font-medium text-gray-700">Selected: </span>
              <span className="text-blue-600">
                {checkInDate ? checkInDate.toLocaleDateString() : 'Check-in'} 
                {checkInDate && checkOutDate && ' → '}
                {checkOutDate ? checkOutDate.toLocaleDateString() : (checkInDate ? ' (Select check-out)' : '')}
              </span>
              {checkInDate && checkOutDate && (
                <span className="ml-2 text-gray-600">
                  ({Math.ceil((checkOutDate - checkInDate) / (1000 * 60 * 60 * 24))} nights)
                </span>
              )}
            </div>
            <button
              type="button"
              onClick={handleClearClick}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Day headers */}
      <div className="grid grid-cols-7 gap-1 mb-1">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1">
        {calendarData.map((date, index) => {
          const availability = getDateAvailability(date);
          return (
            <button
              key={index}
              type="button"
              onClick={(e) => handleDateButtonClick(e, date)}
              className={getDateClassName(date)}
              disabled={isDateDisabled(date)}
              title={availability.message}
            >
              {date.getDate()}
              {getAvailabilityIndicator(date)}
            </button>
          );
        })}
      </div>

      {/* Legend */}
      <div className="mt-2 pt-1 border-t border-gray-200">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span className="font-medium">Availability Legend:</span>
            <span>Click available dates to select check-in, then check-out</span>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-50 border border-green-200 rounded flex items-center justify-center">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              </div>
              <span className="text-green-700">Available</span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-orange-50 border border-orange-200 rounded flex items-center justify-center">
                <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
              </div>
              <span className="text-orange-700">Limited</span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-50 border border-red-200 rounded flex items-center justify-center">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
              </div>
              <span className="text-red-700">Unavailable</span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-600 rounded"></div>
              <span className="text-blue-700">Selected</span>
            </div>
          </div>

          {loadingAvailability && (
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <div className="w-3 h-3 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
              <span>Loading availability...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
export default memo(EnhancedBookingCalendar)